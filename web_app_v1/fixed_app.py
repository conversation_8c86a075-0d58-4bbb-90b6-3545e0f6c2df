#!/usr/bin/env python3
"""
Fixed OAuth Federation App - Simplified and Working Version
"""

import os
import yaml
from pathlib import Path
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Blueprint
from flask_bootstrap import <PERSON><PERSON><PERSON>
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_required, current_user, login_user, logout_user
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import String<PERSON>ield, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email

# Load configuration
def load_config():
    """Load configuration from config.yaml file"""
    config_path = Path(__file__).parent / 'config.yaml'
    if config_path.exists():
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    else:
        return {
            'flask': {
                'secret_key': 'dev-key-for-databricks-genie-federation',
                'debug': True,
                'host': '0.0.0.0',
                'port': 5003
            }
        }

# Load configuration
config = load_config()

# Create Flask application
app = Flask(__name__)
flask_config = config.get('flask', {})
app.config['SECRET_KEY'] = flask_config.get('secret_key', 'dev-key-for-databricks-genie-federation')

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///genie_federation.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
Bootstrap(app)
db = SQLAlchemy(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

# Simple User model
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

    # Flask-Login methods
    def is_authenticated(self):
        return True

    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Simple Organization Config model
class OrganizationConfig(db.Model):
    __tablename__ = 'organization_configs'

    id = db.Column(db.Integer, primary_key=True)
    databricks_host = db.Column(db.String(255), nullable=False)
    databricks_account_id = db.Column(db.String(255), nullable=True)
    genie_space_id = db.Column(db.String(255), nullable=False)
    issuer_url = db.Column(db.String(255), nullable=False)
    audience = db.Column(db.String(100), nullable=False, default='databricks')
    subject_claim = db.Column(db.String(50), nullable=False, default='sub')

    # RSA Keys for JWT signing
    private_key_pem = db.Column(db.Text, nullable=True)
    public_key_pem = db.Column(db.Text, nullable=True)
    key_id = db.Column(db.String(50), nullable=True)

    # Federation policy status
    federation_policy_configured = db.Column(db.Boolean, default=False)
    federation_policy_id = db.Column(db.String(100), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    @classmethod
    def get_current_config(cls):
        return cls.query.first()

# Forms
class LoginForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class ConfigForm(FlaskForm):
    databricks_host = StringField('Databricks Host', validators=[DataRequired()])
    databricks_account_id = StringField('Databricks Account ID (Optional)')
    genie_space_id = StringField('Genie Space ID', validators=[DataRequired()])
    issuer_url = StringField('Identity Provider URL', validators=[DataRequired()])
    audience = StringField('Token Audience', validators=[DataRequired()], default='databricks')
    subject_claim = StringField('Subject Claim', validators=[DataRequired()], default='sub')
    generate_keys = BooleanField('Generate New RSA Key Pair')
    submit = SubmitField('Save Configuration')

# Routes
@app.route('/')
def index():
    """Landing page"""
    if current_user.is_authenticated:
        return redirect(url_for('conversation'))
    else:
        return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if current_user.is_authenticated:
        return redirect(url_for('conversation'))

    form = LoginForm()

    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()

        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Your account has been deactivated.', 'danger')
                return render_template('auth/sso_login.html', form=form)

            login_user(user, remember=form.remember_me.data)
            flash('Login successful!', 'success')

            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('conversation'))
        else:
            flash('Invalid email or password.', 'danger')

    return render_template('auth/sso_login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """Logout"""
    logout_user()
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

@app.route('/conversation')
@login_required
def conversation():
    """Main conversation page"""
    config = OrganizationConfig.get_current_config()
    if not config:
        if current_user.is_admin():
            flash('Please configure the organization settings first.', 'warning')
            return redirect(url_for('admin_config'))
        else:
            flash('System not configured. Please contact an administrator.', 'danger')
            return redirect(url_for('login'))

    return render_template('conversation/index.html')

@app.route('/admin/config', methods=['GET', 'POST'])
@login_required
def admin_config():
    """Admin configuration"""
    if not current_user.is_admin():
        flash('Admin access required.', 'danger')
        return redirect(url_for('login'))

    form = ConfigForm()
    current_config = OrganizationConfig.get_current_config()

    if form.validate_on_submit():
        try:
            if current_config:
                config_obj = current_config
            else:
                config_obj = OrganizationConfig()

            # Update basic configuration
            config_obj.databricks_host = form.databricks_host.data.strip()
            config_obj.databricks_account_id = form.databricks_account_id.data.strip() or None
            config_obj.genie_space_id = form.genie_space_id.data.strip()
            config_obj.issuer_url = form.issuer_url.data.strip()
            config_obj.audience = form.audience.data.strip()
            config_obj.subject_claim = form.subject_claim.data.strip()

            # Generate RSA keys if requested
            if form.generate_keys.data or not config_obj.private_key_pem:
                import secrets
                config_obj.key_id = secrets.token_hex(8)
                config_obj.private_key_pem = "dummy_private_key"  # Simplified for now
                config_obj.public_key_pem = "dummy_public_key"   # Simplified for now
                flash('New RSA key pair generated successfully.', 'success')

            if not current_config:
                db.session.add(config_obj)
            db.session.commit()

            flash('Configuration saved successfully!', 'success')
            return redirect(url_for('admin_config'))

        except Exception as e:
            flash(f'Error saving configuration: {str(e)}', 'danger')

    # Pre-fill form
    if current_config and request.method == 'GET':
        form.databricks_host.data = current_config.databricks_host
        form.databricks_account_id.data = current_config.databricks_account_id
        form.genie_space_id.data = current_config.genie_space_id
        form.issuer_url.data = current_config.issuer_url
        form.audience.data = current_config.audience
        form.subject_claim.data = current_config.subject_claim

    return render_template('admin/config.html', form=form, config=current_config)

# Initialize database
def init_database():
    """Initialize database and create default admin user"""
    with app.app_context():
        db.create_all()

        # Create default admin user if none exists
        if not User.query.filter_by(role='admin').first():
            admin_user = User(
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Created default admin user: <EMAIL> / admin123")

# Initialize database
init_database()

if __name__ == '__main__':
    flask_config = config.get('flask', {})
    debug = flask_config.get('debug', True)
    host = flask_config.get('host', '0.0.0.0')
    port = flask_config.get('port', 5003)

    print(f"🚀 Starting Fixed OAuth Federation App")
    print(f"📍 Access: http://localhost:{port}")
    print(f"🔐 Login: <EMAIL> / admin123")
    print(f"🔗 Identity Provider URL: http://localhost:{port}/oauth")

    app.run(debug=debug, host=host, port=port)
