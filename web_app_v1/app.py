#!/usr/bin/env python3
"""
Databricks Genie Web Application - OAuth Federation Version

This is a modern web application for interacting with the Databricks Genie API.
It provides a professional, enterprise-grade interface for natural language queries
to Databricks data, with OAuth federation authentication.

Features:
- SSO-style login with email/password
- Admin/User role-based access control
- JWT token creation and OAuth federation with Databricks
- Individual user identity preservation in Databricks audit logs

Usage:
    python app.py
"""

import os
import time
import yaml
import requests
from pathlib import Path
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Blueprint
from flask_bootstrap import Bootstrap
from flask_login import LoginManager, login_required, current_user

# Import new authentication and federation modules
from models import db, User, UserSession, OrganizationConfig
from auth_routes import auth_bp
from admin_routes import admin_bp

# Load configuration
def load_config():
    """Load configuration from config.yaml file"""
    config_path = Path(__file__).parent / 'config.yaml'

    if config_path.exists():
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    else:
        # Default configuration
        return {
            'flask': {
                'secret_key': 'dev-key-for-databricks-genie-federation',
                'debug': True,
                'host': '0.0.0.0',
                'port': 5003
            }
        }

# Load configuration
config = load_config()

# Create Flask application
app = Flask(__name__)
flask_config = config.get('flask', {})
app.config['SECRET_KEY'] = flask_config.get('secret_key', 'dev-key-for-databricks-genie-federation')

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///genie_federation.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
Bootstrap(app)
db.init_app(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create main blueprint for conversation routes
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Landing page - redirect based on authentication status"""
    try:
        if current_user.is_authenticated:
            # Check if user has valid session
            session_token = session.get('session_token')
            if session_token:
                user_session = UserSession.get_by_token(session_token)
                if user_session and user_session.is_active:
                    return redirect(url_for('main.conversation'))

            # Invalid session, logout user
            from flask_login import logout_user
            logout_user()
            session.clear()

        return redirect(url_for('auth.login'))
    except Exception as e:
        # Clear any problematic session state
        from flask_login import logout_user
        logout_user()
        session.clear()
        return redirect(url_for('auth.login'))

@main_bp.route('/conversation')
@login_required
def conversation():
    """Main conversation page - requires authentication"""
    # Check if organization is configured
    config = OrganizationConfig.get_current_config()
    if not config:
        if current_user.is_admin():
            flash('Please configure the organization settings first.', 'warning')
            return redirect(url_for('admin.config'))
        else:
            flash('System not configured. Please contact an administrator.', 'danger')
            return redirect(url_for('auth.login'))

    return render_template('conversation/index.html')

@main_bp.route('/api/start-conversation', methods=['POST'])
@login_required
def api_start_conversation():
    """API endpoint to start a new conversation"""
    # Get question from request
    data = request.json
    if not data or ('question' not in data and 'content' not in data):
        return jsonify({'error': 'Question is required'}), 400

    # Support both 'question' and 'content' parameters
    question = data.get('content') if 'content' in data else data.get('question')

    try:
        # Use federation API to start conversation
        from federation_api import federation_api
        response_data = federation_api.start_conversation(question)

        conversation_id = response_data.get('conversation_id')
        message_id = response_data.get('message_id')

        # Wait for the message to complete and get the response
        genie_response = federation_api.wait_for_message(conversation_id, message_id)

        # Return the response with the message content
        return jsonify({
            'success': True,
            'message': question,
            'response': genie_response,
            'conversation_id': conversation_id,
            'message_id': message_id
        })

    except Exception as e:
        return jsonify({
            'error': f'Error starting conversation: {str(e)}'
        }), 500

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(main_bp)

# Initialize database
def init_database():
    """Initialize database and create default admin user"""
    with app.app_context():
        # Create all tables
        db.create_all()

        # Create default admin user if none exists
        if not User.query.filter_by(role='admin').first():
            admin_user = User(
                email='<EMAIL>',
                role='admin',
                databricks_username='<EMAIL>'
            )
            admin_user.set_password('admin123')  # Change this in production!
            db.session.add(admin_user)
            db.session.commit()
            print("Created default admin user: <EMAIL> / admin123")

# Initialize database when module is imported
init_database()

# Run the application
if __name__ == '__main__':
    # Get Flask configuration from config
    flask_config = config.get('flask', {})
    debug = flask_config.get('debug', True)
    host = flask_config.get('host', '0.0.0.0')
    port = flask_config.get('port', 5003)

    print(f"🚀 Starting Databricks Genie Web App with OAuth Federation")
    print(f"📍 Access the application at: http://localhost:{port}")
    print(f"🔐 SSO-style authentication with JWT federation")
    print(f"👨‍💼 Admin panel available for configuration")
    print(f"🔗 Individual user identity preserved in Databricks")

    app.run(debug=debug, host=host, port=port)
