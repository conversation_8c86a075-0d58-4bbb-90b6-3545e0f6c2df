#!/usr/bin/env python3
"""
Test script to verify all OAuth Federation endpoints are working
"""

import requests
import json
import time
import sys
from urllib.parse import urljoin

# Configuration
BASE_URL = "http://localhost:5003"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def test_endpoint(url, method="GET", data=None, headers=None, expected_status=200):
    """Test an endpoint and return result"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, data=data, headers=headers, timeout=10)
        
        success = response.status_code == expected_status
        return {
            "success": success,
            "status_code": response.status_code,
            "url": url,
            "method": method
        }
    except requests.RequestException as e:
        return {
            "success": False,
            "status_code": 0,
            "url": url,
            "method": method,
            "error": str(e)
        }

def main():
    print("🧪 OAuth Federation Endpoint Testing")
    print("=" * 50)
    
    # Test basic connectivity
    print("\n📡 Testing Basic Connectivity...")
    
    tests = [
        # Basic endpoints
        {"url": f"{BASE_URL}/", "name": "Home Page"},
        {"url": f"{BASE_URL}/auth/login", "name": "Login Page"},
        
        # OpenID Connect endpoints
        {"url": f"{BASE_URL}/.well-known/openid-configuration", "name": "OpenID Configuration"},
        {"url": f"{BASE_URL}/.well-known/jwks.json", "name": "JWKS Endpoint"},
        
        # Admin endpoints (will redirect to login if not authenticated)
        {"url": f"{BASE_URL}/admin/dashboard", "name": "Admin Dashboard", "expected_status": 302},
        {"url": f"{BASE_URL}/admin/config", "name": "Admin Config", "expected_status": 302},
        {"url": f"{BASE_URL}/admin/users", "name": "Admin Users", "expected_status": 302},
        
        # API endpoints (will require authentication)
        {"url": f"{BASE_URL}/api/start-conversation", "method": "POST", "name": "Start Conversation API", "expected_status": 401},
    ]
    
    results = []
    for test in tests:
        print(f"Testing {test['name']}...", end=" ")
        
        result = test_endpoint(
            test["url"], 
            method=test.get("method", "GET"),
            expected_status=test.get("expected_status", 200)
        )
        
        if result["success"]:
            print("✅ PASS")
        else:
            print(f"❌ FAIL ({result['status_code']})")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        results.append({**test, **result})
    
    # Summary
    print("\n📊 Test Summary:")
    print("-" * 30)
    
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All endpoints are working correctly!")
        print("\n📋 Next Steps:")
        print("1. Open browser to: http://localhost:5003")
        print("2. Login with: <EMAIL> / admin123")
        print("3. Configure organization settings")
        print("4. Set Identity Provider URL to: http://localhost:5003/oauth")
    else:
        print("\n⚠️  Some endpoints failed. Check if the app is running:")
        print("   python app.py")
    
    # Test specific OAuth Federation features
    print("\n🔐 OAuth Federation Feature Check:")
    print("-" * 40)
    
    # Check if OpenID configuration is valid
    try:
        response = requests.get(f"{BASE_URL}/.well-known/openid-configuration", timeout=5)
        if response.status_code == 200:
            config = response.json()
            print("✅ OpenID Configuration:")
            print(f"   Issuer: {config.get('issuer', 'Not found')}")
            print(f"   JWKS URI: {config.get('jwks_uri', 'Not found')}")
        else:
            print("❌ OpenID Configuration not accessible")
    except Exception as e:
        print(f"❌ OpenID Configuration error: {e}")
    
    # Check JWKS endpoint
    try:
        response = requests.get(f"{BASE_URL}/.well-known/jwks.json", timeout=5)
        if response.status_code == 200:
            jwks = response.json()
            keys = jwks.get('keys', [])
            print(f"✅ JWKS Endpoint: {len(keys)} key(s) available")
        else:
            print("❌ JWKS Endpoint not accessible")
    except Exception as e:
        print(f"❌ JWKS Endpoint error: {e}")

if __name__ == "__main__":
    main()
