#!/usr/bin/env python3
"""
Reset OAuth Federation App - Clear database and restart fresh
"""

import os
import sys
from pathlib import Path

def reset_database():
    """Reset the database to clean state"""
    print("🔄 Resetting OAuth Federation Database...")
    
    # Remove existing database
    db_path = Path("instance/genie_federation.db")
    if db_path.exists():
        os.remove(db_path)
        print("✅ Removed existing database")
    
    # Ensure instance directory exists
    instance_dir = Path("instance")
    instance_dir.mkdir(exist_ok=True)
    
    print("✅ Database reset complete")

def test_imports():
    """Test if all modules can be imported"""
    print("\n🧪 Testing Module Imports...")
    
    try:
        from models import db, User, OrganizationConfig, UserSession
        print("✅ Models imported successfully")
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False
    
    try:
        from auth_federation import jwt_provider, federation_client
        print("✅ Auth federation imported successfully")
    except Exception as e:
        print(f"❌ Auth federation import failed: {e}")
        return False
    
    try:
        from auth_routes import auth_bp
        print("✅ Auth routes imported successfully")
    except Exception as e:
        print(f"❌ Auth routes import failed: {e}")
        return False
    
    try:
        from admin_routes import admin_bp
        print("✅ Admin routes imported successfully")
    except Exception as e:
        print(f"❌ Admin routes import failed: {e}")
        return False
    
    return True

def initialize_app():
    """Initialize the app with fresh database"""
    print("\n🚀 Initializing Fresh Application...")
    
    try:
        from app import app, init_database
        
        with app.app_context():
            # Initialize database
            init_database()
            print("✅ Database initialized with default admin user")
            
            # Verify admin user
            from models import User
            admin = User.query.filter_by(role='admin').first()
            if admin:
                print(f"✅ Admin user created: {admin.email}")
                print("   Default password: admin123")
            else:
                print("❌ Admin user not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ App initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main reset function"""
    print("🔧 OAuth Federation App Reset Tool")
    print("=" * 40)
    
    # Step 1: Reset database
    reset_database()
    
    # Step 2: Test imports
    if not test_imports():
        print("\n❌ Import test failed. Please check your dependencies.")
        return False
    
    # Step 3: Initialize app
    if not initialize_app():
        print("\n❌ App initialization failed.")
        return False
    
    print("\n🎉 Reset Complete!")
    print("=" * 40)
    print("✅ Database reset and initialized")
    print("✅ Default admin user created")
    print("✅ All modules working correctly")
    print()
    print("📋 Next Steps:")
    print("1. Run: python app.py")
    print("2. Open: http://localhost:5003")
    print("3. Login: <EMAIL> / admin123")
    print("4. Configure organization settings")
    print()
    print("🔐 Identity Provider URL: http://localhost:5003/oauth")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
