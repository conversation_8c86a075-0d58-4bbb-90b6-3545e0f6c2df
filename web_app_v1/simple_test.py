#!/usr/bin/env python3
"""
Simple test to diagnose the issue
"""

print("Starting simple test...")

try:
    print("Testing Flask import...")
    from flask import Flask
    print("✅ Flask imported")
    
    print("Testing yaml import...")
    import yaml
    print("✅ yaml imported")
    
    print("Testing requests import...")
    import requests
    print("✅ requests imported")
    
    print("Testing SQLAlchemy import...")
    from flask_sqlalchemy import SQLAlchemy
    print("✅ SQLAlchemy imported")
    
    print("Testing Flask-Login import...")
    from flask_login import LoginManager
    print("✅ Flask-Login imported")
    
    print("Testing JWT import...")
    import jwt
    print("✅ JWT imported")
    
    print("Testing cryptography import...")
    from cryptography.hazmat.primitives import hashes
    print("✅ cryptography imported")
    
    print("\n✅ All basic imports successful!")
    
    print("\nTesting app creation...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db = SQLAlchemy()
    db.init_app(app)
    
    print("✅ Basic Flask app created successfully!")
    
    print("\n🎉 All tests passed! The issue might be elsewhere.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
