# Databricks Genie API Web Application Configuration Example
# Rename this file to config.yaml and fill in your values

# Authentication Configuration
auth:
  # Databricks workspace hostname (without https://)
  # Example: dbc-123abc45-6def.cloud.databricks.com
  host: "dbc-620d1468-0f52.cloud.databricks.com"

  # Genie Space ID
  # This can be found in the URL of your Genie Space
  # Example: If your Genie Space URL is https://example.databricks.com/genie/rooms/12ab345cd6789000ef6a2fb844ba2d31?o=****************
  # Then your space_id is 12ab345cd6789000ef6a2fb844ba2d31?o=****************
  space_id: "01f02f16a7b11b36a04e4353814a5699?o=****************"

  # OAuth Authentication (Required)
  # Create a service principal in your Databricks workspace and generate an OAuth secret
  # See: https://docs.databricks.com/dev-tools/auth/oauth-m2m
  client_id: "9e4f6f53-e2fb-4f7c-a7ce-bf1889db9bdc"
  client_secret: "dosebc6041c5004a2289159df91d98a97a66"

  # Only needed for account-level operations (leave empty for workspace-level operations)
  account_id: ""

# UI Configuration
ui:
  # Application title
  title: "Databricks Genie"

  # Theme colors (hex values)
  colors:
    primary: "#FF3621"    # Databricks Red
    secondary: "#1B3139"  # Databricks Navy
    accent: "#F9F7F4"     # Databricks Oat Light
    background: "#FFFFFF" # White
    text: "#1B3139"       # Dark text

  # Logo paths (relative to static/img directory)
  logos:
    databricks: "databricks.png"
    tudip: "tudip.jpeg"
    favicon: "favicon.svg"

  # Logo sizes
  logo_config:
    databricks_height: 60  # Height in pixels
    tudip_height: 60       # Height in pixels

# Flask Application Configuration
flask:
  # Secret key for session encryption
  secret_key: "prod-key-databricks-genie-tudip-2024"

  # Debug mode (set to false in production)
  debug: false

  # Host to bind to
  host: "0.0.0.0"

  # Port to listen on
  port: 5003
