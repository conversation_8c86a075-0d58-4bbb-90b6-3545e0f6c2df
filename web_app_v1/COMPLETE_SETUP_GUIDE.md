# 🚀 Complete OAuth Federation Setup Guide

## 📍 **CORRECT IDENTITY PROVIDER URL**

### **For Your Current Setup:**
```
Identity Provider URL: http://localhost:5003/oauth
```

### **For Production Deployment:**
```
Identity Provider URL: https://databricks-accelerator.tudip.ai/oauth
```

## 🔧 **Step-by-Step Configuration**

### **Step 1: Start the Application**
```bash
cd "/home/<USER>/Documents/Genie_API_Exploration /web_app_v1"
python app.py
```

The app will start on port 5003 (as configured).

### **Step 2: Access the Application**
Open your browser and go to: `http://localhost:5003`

### **Step 3: Login as Admin**
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### **Step 4: Fill Configuration Form**

#### 🏢 **Organization Settings**
```
Databricks Host: dbc-620d1468-0f52.cloud.databricks.com
Genie Space ID: 01f02f16a7b11b36a04e4353814a5699?o=****************
Databricks Account ID: [Leave empty for workspace-level]
```

#### 🔐 **Identity Provider Configuration**
```
Identity Provider URL: http://localhost:5003/oauth
Token Audience: databricks
Subject Claim: sub
```

#### 🔒 **Security Configuration**
```
✅ Check: "Generate New RSA Key Pair"
```

### **Step 5: Save Configuration**
Click "Save Configuration" button.

## 🎯 **What Happens After Configuration**

### **1. Federation Setup Guide Appears**
After saving, you'll see a page with:
- **JWKS URL**: `http://localhost:5003/oauth/.well-known/jwks.json`
- **OpenID Config**: `http://localhost:5003/oauth/.well-known/openid-configuration`
- **Federation Policy JSON**: Ready-to-use JSON

### **2. Create Federation Policy in Databricks**
Copy the provided JSON and run:
```bash
databricks account federation-policies create --json '{
  "oidc_policy": {
    "issuer": "http://localhost:5003/oauth",
    "audiences": ["databricks"],
    "subject_claim": "sub"
  }
}'
```

### **3. Create Users**
Go to Admin > Users and create user accounts:
- Email: <EMAIL>
- Password: user123
- Role: user
- Databricks Username: <EMAIL>

## 🔍 **All Available Endpoints**

### **Authentication Endpoints**
- `GET /auth/login` - Login page
- `POST /auth/login` - Process login
- `GET /auth/logout` - Logout
- `GET /auth/register` - Registration page (if enabled)

### **Admin Endpoints**
- `GET /admin/dashboard` - Admin dashboard
- `GET /admin/config` - Organization configuration
- `GET /admin/users` - User management
- `GET /admin/federation-setup-guide` - Federation setup instructions

### **OpenID Connect Endpoints**
- `GET /.well-known/openid-configuration` - OpenID Provider Configuration
- `GET /.well-known/jwks.json` - JSON Web Key Set

### **Main Application Endpoints**
- `GET /` - Home page (redirects to login or conversation)
- `GET /conversation` - Main Genie interface
- `POST /api/start-conversation` - Start new conversation
- `POST /api/send-message` - Send message to Genie

## ✅ **Testing the System**

### **1. Test Login**
- Go to `http://localhost:5003`
- Login with admin credentials
- Should redirect to admin dashboard

### **2. Test Configuration**
- Fill in the configuration form
- Save configuration
- Should see federation setup guide

### **3. Test User Creation**
- Go to Admin > Users
- Create a new user
- Login with new user credentials

### **4. Test Genie Interface**
- Login as regular user
- Go to conversation page
- Ask a question: "Show me the top 10 customers"
- Should get response from Genie

### **5. Verify User Identity**
- Check Databricks audit logs
- Should show actual user email, not service principal

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **App won't start**
   - Check if port 5003 is available
   - Install dependencies: `pip install -r requirements.txt`

2. **Configuration not saving**
   - Check database permissions
   - Verify all required fields are filled

3. **Federation policy errors**
   - Ensure Databricks CLI is configured
   - Check network connectivity to Databricks

4. **Token exchange failures**
   - Verify federation policy is active in Databricks
   - Check JWKS endpoint is accessible

## 🔐 **Security Notes**

- Change default admin password immediately
- Use HTTPS in production
- Secure database with proper credentials
- Monitor audit logs regularly

## 📞 **Next Steps After Configuration**

1. **Complete federation setup** in Databricks
2. **Create user accounts** for your team
3. **Test the complete flow** with a regular user
4. **Verify audit logs** show individual user identities
5. **Deploy to production** with HTTPS
