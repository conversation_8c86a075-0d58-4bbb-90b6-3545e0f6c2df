{% extends "simple_base.html" %}

{% block title %}Login - Databricks Genie OAuth Federation{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header text-center py-4">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt text-primary mr-2"></i>
                        OAuth Login
                    </h4>
                    <p class="text-muted small mb-0">Databricks Genie Federation</p>
                </div>
                <div class="card-body p-4">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="form-group">
                            {{ form.email.label(class="form-label font-weight-bold") }}
                            {{ form.email(class="form-control form-control-lg", placeholder="Enter your email") }}
                            {% if form.email.errors %}
                                <div class="text-danger">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.password.label(class="form-label font-weight-bold") }}
                            {{ form.password(class="form-control form-control-lg", placeholder="Enter your password") }}
                            {% if form.password.errors %}
                                <div class="text-danger">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                {{ form.remember_me(class="custom-control-input") }}
                                {{ form.remember_me.label(class="custom-control-label") }}
                            </div>
                        </div>

                        <div class="form-group mb-0">
                            {{ form.submit(class="btn btn-primary btn-lg btn-block") }}
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle mr-1"></i>
                        Default: <EMAIL> / admin123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
