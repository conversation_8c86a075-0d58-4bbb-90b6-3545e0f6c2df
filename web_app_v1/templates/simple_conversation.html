{% extends "simple_base.html" %}

{% block title %}Conversation - Databricks Genie{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h5 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-comment-dots mr-2"></i> Databricks Genie Conversation
                    </h5>
                    <p class="text-muted small mb-0">Ask questions about your data using natural language</p>
                </div>
                <div class="card-body">
                    <div class="alert alert-success" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-check-circle mr-2"></i> OAuth Federation Active
                        </h6>
                        <p class="mb-0">
                            You are successfully authenticated via OAuth federation. 
                            Your identity (<strong>{{ current_user.email }}</strong>) will be preserved in Databricks audit logs.
                        </p>
                    </div>

                    <div class="alert alert-info" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle mr-2"></i> System Ready
                        </h6>
                        <p class="mb-0">
                            The OAuth federation system is configured and ready to use. 
                            In a full implementation, this page would contain the Genie conversation interface.
                        </p>
                    </div>

                    <div class="text-center py-4">
                        <i class="fas fa-robot fa-4x text-primary mb-3"></i>
                        <h4>Genie Conversation Interface</h4>
                        <p class="text-muted">
                            This is where users would interact with Databricks Genie using natural language queries.
                        </p>
                        
                        <div class="mt-4">
                            <h6 class="text-success">✅ OAuth Federation Benefits:</h6>
                            <ul class="list-unstyled text-left d-inline-block">
                                <li><i class="fas fa-check text-success mr-2"></i> Individual user identity preserved</li>
                                <li><i class="fas fa-check text-success mr-2"></i> No shared service principal</li>
                                <li><i class="fas fa-check text-success mr-2"></i> Proper audit logging</li>
                                <li><i class="fas fa-check text-success mr-2"></i> SSO-style authentication</li>
                                <li><i class="fas fa-check text-success mr-2"></i> JWT token-based security</li>
                            </ul>
                        </div>

                        {% if current_user.is_admin() %}
                        <div class="mt-4">
                            <a href="{{ url_for('admin_config') }}" class="btn btn-outline-primary">
                                <i class="fas fa-cog mr-1"></i> Admin Configuration
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
