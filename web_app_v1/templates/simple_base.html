<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Databricks Genie OAuth Federation{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        .logo {
            max-height: 45px;
            width: auto;
        }
        .footer {
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            margin-top: auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                    <span class="font-weight-bold">🚀 Databricks Genie</span>
                </a>

                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ml-auto">
                        {% if current_user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('conversation') }}">
                                    <i class="fas fa-comment-dots mr-1"></i> Conversation
                                </a>
                            </li>
                            <li class="nav-item">
                                <span class="nav-link">
                                    <span class="badge badge-pill badge-success">OAuth</span>
                                </span>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="accountDropdown" role="button" data-toggle="dropdown">
                                    <i class="fas fa-user-circle mr-1"></i> {{ current_user.email }}
                                </a>
                                <div class="dropdown-menu dropdown-menu-right">
                                    {% if current_user.is_admin() %}
                                    <a class="dropdown-item" href="{{ url_for('admin_config') }}">
                                        <i class="fas fa-cog mr-1"></i> Admin Config
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    {% endif %}
                                    <a class="dropdown-item" href="{{ url_for('logout') }}">
                                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                                    </a>
                                </div>
                            </li>
                        {% endif %}
                    </ul>
                </div>

                <div class="ml-3 d-flex align-items-center">
                    <span class="text-muted">Tudip Technologies</span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="py-4" style="min-height: calc(100vh - 200px);">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-4 bg-white border-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-left">
                    <p class="text-muted mb-0">
                        <small>🚀 Databricks Genie OAuth Federation</small>
                    </p>
                </div>
                <div class="col-md-6 text-center text-md-right">
                    <p class="text-muted mb-0">
                        <small>&copy; 2025 Tudip Technologies Pvt Ltd</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
