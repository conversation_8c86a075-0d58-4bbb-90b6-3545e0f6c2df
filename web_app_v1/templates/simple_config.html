{% extends "simple_base.html" %}

{% block title %}Configuration - Admin{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h5 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog mr-2"></i> Organization Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Databricks Configuration -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-database mr-2"></i> Databricks Configuration
                        </h6>

                        <div class="form-group">
                            {{ form.databricks_host.label(class="form-label font-weight-bold") }}
                            {{ form.databricks_host(class="form-control") }}
                            <small class="form-text text-muted">
                                Your Databricks workspace URL without https:// (e.g., dbc-123abc45-6def.cloud.databricks.com)
                            </small>
                        </div>

                        <div class="form-group">
                            {{ form.databricks_account_id.label(class="form-label font-weight-bold") }}
                            {{ form.databricks_account_id(class="form-control") }}
                            <small class="form-text text-muted">
                                Optional: For account-level federation. Leave empty for workspace-level federation.
                            </small>
                        </div>

                        <div class="form-group">
                            {{ form.genie_space_id.label(class="form-label font-weight-bold") }}
                            {{ form.genie_space_id(class="form-control") }}
                            <small class="form-text text-muted">
                                Found in the URL of your Genie space (e.g., 12ab345cd6789000ef6a2fb844ba2d31?o=****************)
                            </small>
                        </div>

                        <hr class="my-4">

                        <!-- Identity Provider Configuration -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-shield-alt mr-2"></i> Identity Provider Configuration
                        </h6>

                        <div class="form-group">
                            {{ form.issuer_url.label(class="form-label font-weight-bold") }}
                            {{ form.issuer_url(class="form-control") }}
                            <small class="form-text text-muted">
                                The base URL for your identity provider (this application). Used in JWT tokens.
                            </small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.audience.label(class="form-label font-weight-bold") }}
                                    {{ form.audience(class="form-control") }}
                                    <small class="form-text text-muted">Token audience (usually 'databricks')</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.subject_claim.label(class="form-label font-weight-bold") }}
                                    {{ form.subject_claim(class="form-control") }}
                                    <small class="form-text text-muted">JWT claim for user identity (usually 'sub')</small>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Security Configuration -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-key mr-2"></i> Security Configuration
                        </h6>

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                {{ form.generate_keys(class="custom-control-input") }}
                                {{ form.generate_keys.label(class="custom-control-label font-weight-bold") }}
                            </div>
                            <small class="form-text text-muted">
                                Check this to generate a new RSA key pair for JWT signing.
                                {% if config and config.private_key_pem %}
                                    Current key ID: <code>{{ config.key_id }}</code>
                                {% else %}
                                    No keys configured yet.
                                {% endif %}
                            </small>
                        </div>

                        <div class="form-group mt-4">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('conversation') }}" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Configuration Status -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Configuration Status</h6>
                </div>
                <div class="card-body">
                    {% if config %}
                    <div class="mb-3">
                        <h6 class="text-success">
                            <i class="fas fa-check-circle mr-1"></i> Configuration Exists
                        </h6>
                        <p class="text-muted small">
                            Last updated: {{ config.updated_at.strftime('%Y-%m-%d %H:%M') if config.updated_at else 'Just now' }}
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6 class="{% if config.private_key_pem %}text-success{% else %}text-warning{% endif %}">
                            <i class="fas fa-{% if config.private_key_pem %}check-circle{% else %}exclamation-triangle{% endif %} mr-1"></i> 
                            RSA Keys
                        </h6>
                        {% if config.private_key_pem %}
                            <p class="text-muted small">Key ID: <code>{{ config.key_id }}</code></p>
                        {% else %}
                            <p class="text-muted small">No RSA keys configured</p>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="text-info">
                            <i class="fas fa-link mr-1"></i> Identity Provider URL
                        </h6>
                        <p class="text-muted small">
                            <code>{{ config.issuer_url }}</code>
                        </p>
                    </div>
                    {% else %}
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h6 class="text-warning">No Configuration</h6>
                        <p class="text-muted small">
                            Please fill out the form to configure the system.
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>

            {% if not config %}
            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Setup</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">Use these values for quick setup:</p>
                    <div class="mb-2">
                        <strong>Databricks Host:</strong><br>
                        <small class="text-muted">dbc-620d1468-0f52.cloud.databricks.com</small>
                    </div>
                    <div class="mb-2">
                        <strong>Genie Space ID:</strong><br>
                        <small class="text-muted">01f02f16a7b11b36a04e4353814a5699?o=1883526265026134</small>
                    </div>
                    <div class="mb-2">
                        <strong>Identity Provider URL:</strong><br>
                        <small class="text-muted">http://localhost:5003/oauth</small>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
